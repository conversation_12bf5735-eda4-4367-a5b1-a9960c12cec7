USE membercentral;
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @componentID int, @methodID int, @adminRTFID int;

	SELECT @adminRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

	EXEC dbo.ajax_addComponentMethodRights
		@componentName='ADMINREFERRALS',
		@requestCFC='model.admin.referrals.referrals',
		@methodName='getAvailableAndSelectedReferralGroupSetsJSON',
		@resourceTypeFunctionIDList=@adminRTFID,
		@componentID=@componentID OUTPUT,
		@methodID=@methodID OUTPUT;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @componentID int, @methodID int, @adminRTFID int;

	SELECT @adminRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

	EXEC dbo.ajax_addComponentMethodRights
		@componentName='ADMMEMBERDIRECTORY',
		@requestCFC='model.admin.memberDirectory.memberDirectoryAdmin',
		@methodName='getAvailableAndSelectedMemberDirectoryGroupSetsJSON',
		@resourceTypeFunctionIDList=@adminRTFID,
		@componentID=@componentID OUTPUT,
		@methodID=@methodID OUTPUT;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO

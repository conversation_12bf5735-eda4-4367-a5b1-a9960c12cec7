<cfset local.selectedTab = arguments.event.getTrimValue("tab","settingsTab")>
<cfif arguments.event.getTrimValue("lockTab","false")>
	<cfset local.lockTab = local.selectedTab>
<cfelse>
	<cfset local.lockTab = "">
</cfif>
<cfsavecontent variable="local.editTabsJS">
	<cfoutput>
	<script language="javascript">
		var gridInitArray = new Array();
		gridInitArray["settingsTab"] = false;
		gridInitArray["classificationsTab"] = false;
		gridInitArray["sortTab"] = false;

		function onTabChangeHandler(ActiveTab) {
			if (!gridInitArray[ActiveTab.id]) {
				gridInitArray[ActiveTab.id] = true;
				switch(ActiveTab.id) {
					case "sortTab":
						initCurrentSortOptionsTable();
						initAvailSortOptionsTable();
						break;
				}
			}
		}

		$(function() {
			mca_initNavPills('memberDirectoryPills', '#local.selectedTab#', '#local.lockTab#', onTabChangeHandler);
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.editTabsJS)#">

<cfoutput>
<ul class="nav nav-pills nav-pills-dotted memberDirectoryPills" role="tablist" id="memberDirectoryPills">
	<cfset local.thisTabID = "settingsTab">
	<cfset local.thisTabName = "settings">
	<li class="nav-item"><a 
		id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
		class="nav-link memberDirectoryPills" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Settings</a>
	</li>

	<cfset local.thisTabID = "classificationsTab">
	<cfset local.thisTabName = "classifications">
	<li class="nav-item"><a 
		id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
		class="nav-link memberDirectoryPills" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Classifications</a>
	</li>

	<cfset local.thisTabID = "sortTab">
	<cfset local.thisTabName = "sortOptions">
	<li class="nav-item"><a 
		id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
		class="nav-link memberDirectoryPills" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Sort Options</a>
	</li>
</ul>
<div class="tab-content mc_tabcontent p-3 pb-0" id="pills-tabContent">
	<div class="tab-pane fade" id="pills-settingsTab" role="tabpanel" aria-labelledby="settingsTab">
		<cfinclude template="frm_edit_settings.cfm">
	</div>
	<div class="tab-pane fade" id="pills-classificationsTab" role="tabpanel" aria-labelledby="classificationsTab">
		<cfinclude template="frm_edit_classifications.cfm">
	</div>
	<div class="tab-pane fade" id="pills-sortTab" role="tabpanel" aria-labelledby="sortTab">
		<cfinclude template="frm_edit_sort.cfm">
	</div>
</div>
</cfoutput>
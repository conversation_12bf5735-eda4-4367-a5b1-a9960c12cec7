<cfsavecontent variable="local.generalTabJS">
	<cfoutput>
	<cfif local.strWorkspaceProjectFields.hasFields>
		<script type="text/javascript" src="/assets/common/javascript/resourceFields.js"></script>
		#local.strWorkspaceProjectFields.head#
	</cfif>
	<cfif arguments.event.getValue('projectID') gt 0>
		<script type="text/javascript">
			var #ToScript(local.permsGotoLink,"mca_perms_link")#
			function addEmailTemplate(tid) {
				var addResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						$('##stepETTasks_sel').html(r.selgrid);
						$('##stepETTasks_avail').html(r.availgrid);
					} else {
						alert('Some error occurred while adding email template. Please try again or contact MemberCentral for assistance.');
					}
				};

				$('##stepETTasks_avail').html('<tr><td colspan="2"><div class="c"><i class="fa-light fa-circle-notch fa-spin fa-2x"></i> Please Wait...</div></td></tr>');

				var objParams = { projectID:#arguments.event.getValue('projectID')#, templateID:tid };
				TS_AJX('ADMINPROJECT','addProjectEmailTemplate',objParams,addResult,addResult,10000,addResult);
			}
			function removeEmailTemplate(ptid) {
				var removeResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						$('##stepETTasks_sel').html(r.selgrid);
						$('##stepETTasks_avail').html(r.availgrid);
					} else {
						alert('Some error occurred while adding removing template. Please try again or contact MemberCentral for assistance.');
					}
				};

				$('##stepETTasks_sel').html('<tr><td colspan="2"><div class="c"><i class="fa-light fa-circle-notch fa-spin fa-2x"></i> Please Wait...</div></td></tr>');

				var objParams = { projectID:#arguments.event.getValue('projectID')#, projectTemplateID:ptid };
				TS_AJX('ADMINPROJECT','removeProjectEmailTemplate',objParams,removeResult,removeResult,10000,removeResult);
			}
			function reloadDetailsClassificationsTable(){
				loadGroupSetGrids_projectDetailsGroupSets();
			}
		</script>
	</cfif>
	<script type="text/javascript">
		function validateGeneralInfo() {
			mca_hideAlert(['err_proj_general','err_onholdnotify_emails','err_payprofileaddnotify_emails']);
			var arrReq = new Array();

			if($('##projectName').val().trim().length == 0) 
				arrReq[arrReq.length] = 'Enter the project name.';
			
			var projectDesc = "";
			if(CKEDITOR.instances['projectDesc'] != null)
				projectDesc = CKEDITOR.instances['projectDesc'].getData().trim();
			else 
				projectDesc = $('textarea[name="projectDesc"]').val().trim();

			if(projectDesc.length == 0)
				arrReq[arrReq.length] = 'Project description is required.';

			if (Number($('input[name="hiddenFromWorkspace"]:checked').val()) == 0) {
				if ($('##accessStartDate').val() == '') arrReq[arrReq.length] = 'Enter the Start Date.';
				if ($('##accessEndDate').val() == '') arrReq[arrReq.length] = 'Enter the End Date.';

				var accessExpirationContent = "";
				if(CKEDITOR.instances['accessExpirationContent'] != null)
					accessExpirationContent = CKEDITOR.instances['accessExpirationContent'].getData().trim();
				else 
					accessExpirationContent = $('textarea[name="accessExpirationContent"]').val().trim();

				if(accessExpirationContent.length == 0)
					arrReq[arrReq.length] = 'Expiration Message is required.';
			}

			if (Number($('input[name="requestPayProfile"]:checked').val()) == 1) {
				if($('##profileID').val() == 0)
					arrReq[arrReq.length] = 'Select a Payment Profile.';

				var requestPayProfileContent = "";
				if(CKEDITOR.instances['requestPayProfileContent'] != null)
					requestPayProfileContent = CKEDITOR.instances['requestPayProfileContent'].getData().trim();
				else 
					requestPayProfileContent = $('textarea[name="requestPayProfileContent"]').val().trim();

				if(requestPayProfileContent.length == 0)
					arrReq[arrReq.length] = 'Payment Instructions is required.';
			}

			var errorMsgArray = [];
			<cfif local.strWorkspaceProjectFields.hasFields>
				#local.strWorkspaceProjectFields.jsValidation#
			</cfif>

			/*drop empty elements*/
			var finalErrors = $.map(errorMsgArray, function(thisError){
				if (thisError.length) return thisError;
				else return null;
			});
			arrReq = arrReq.concat(finalErrors);

			if (arrReq.length) {
				mca_showAlert('err_proj_general', arrReq.join('<br/>'));
				return false;
			}

			$('##btnSavePrjGenDetails').prop('disabled',true);
			return true;
		}
		function toggleVisibleSettings(x) {
			$('div.visibleWorkSpaceRow').toggleClass('d-none', (x == 1 ? true : false)); /* x=1 is hidden*/
		}
		function togglePayProfileSettings(x) {
			$('div.payProfileRow').toggleClass('d-none', (x == 1 ? false : true));
		}

		$(function() {
			mca_setupDateTimePickerRangeFields('accessStartDate','accessEndDate',30);
			mca_setupCalendarIcons('frmProjectsBasicInfo');
			mca_setupTagsInput('onHoldNotificationEmails', 'err_onholdnotify_emails', "#application.regEx.email#", 'email address');
			mca_setupTagsInput('payProfileNotificationEmails', 'err_payprofileaddnotify_emails', "#application.regEx.email#", 'email address');
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.generalTabJS#">

<cfoutput>
<cfform action="#local.formLink#" method="POST" id="frmProjectsBasicInfo" name="frmProjectsBasicInfo" onsubmit="return validateGeneralInfo();">
	<input type="hidden" name="projectID" value="#arguments.event.getValue('projectID')#">
	<input type="hidden" name="workspaceID" value="#arguments.event.getValue('workspaceID')#">

	<div id="err_proj_general" class="alert alert-danger mb-2 d-none"></div>
	<div class="text-right">
		<cfif arguments.event.getValue('projectID') gt 0>
			<button type="button" name="btnProjectPerms" class="btn btn-sm btn-secondary" onclick="mca_showPermissions(#arguments.event.getValue('siteResourceID')#,'#encodeForJavaScript(arguments.event.getValue('projectName'))#')">Permissions</button>
		</cfif>
		<button type="submit" name="btnSavePrjGenDetails" id="btnSavePrjGenDetails" class="btn btn-sm btn-primary">Save Details</button>
	</div>

	<h5>Basic Project Information</h5>

	<div class="form-group row">
		<label class="col-sm-3 col-form-label">Workspace:</label>
		<div class="col-sm-9 col-form-label">#arguments.event.getValue('appInstanceName')#</div>
	</div>

	<div class="form-group row">
		<label for="projectName" class="col-sm-3 col-form-label-sm font-size-md">Project Name: *</label>
		<div class="col-sm-9">
			<input type="text" name="projectName" id="projectName" class="form-control form-control-sm" value="#arguments.event.getValue('projectName')#">
		</div>
	</div>

	<div class="form-group row">
		<label class="col-sm-3 col-form-label-sm font-size-md pt-0">Visible in Front-End Workspace: *</label>
		<div class="col-sm-9">
			<div class="form-check form-check-inline">
				<input type="radio" name="hiddenFromWorkspace" id="hiddenFromWorkspaceYes" class="form-check-input" value="0" onclick="toggleVisibleSettings(0);"<cfif arguments.event.getValue('hiddenFromWorkspace') is 0> checked</cfif>>
				<label class="form-check-label" for="hiddenFromWorkspaceYes">Yes</label>
			</div>
			<div class="form-check form-check-inline">
				<input type="radio" name="hiddenFromWorkspace" id="hiddenFromWorkspaceNo" class="form-check-input" value="1" onclick="toggleVisibleSettings(1);"<cfif arguments.event.getValue('hiddenFromWorkspace') is 1> checked</cfif>>
				<label class="form-check-label" for="hiddenFromWorkspaceNo">No</label>
			</div>
		</div>
	</div>

	<div class="visibleWorkSpaceRow<cfif arguments.event.getValue('hiddenFromWorkspace') is 1> d-none</cfif>">
		<div class="form-group row">
			<label class="col-sm-3 col-form-label-sm font-size-md">Dates Available: *</label>
			<div class="col-sm-9">
				<div class="row">
					<div class="col-sm-5 pr-md-0">
						<div class="input-group input-group-sm">
							<input type="text" name="accessStartDate" id="accessStartDate" value="#dateFormat(arguments.event.getValue('accessStartDate'),'m/d/yyyy')# - #timeFormat(arguments.event.getValue('accessStartDate'),'h:mm tt')#" class="form-control form-control-sm dateControl" placeholder="From">
							<div class="input-group-append">
								<span class="input-group-text cursor-pointer calendar-button" data-target="accessStartDate"><i class="fa-solid fa-calendar"></i></span>
							</div>
						</div>
					</div>
					<div class="col-md col-sm-5 pr-md-0">
						<div class="input-group input-group-sm">
							<input type="text" name="accessEndDate" id="accessEndDate" value="#dateFormat(arguments.event.getValue('accessEndDate'),'m/d/yyyy')# - #timeFormat(arguments.event.getValue('accessEndDate'),'h:mm tt')#" class="form-control form-control-sm dateControl" placeholder="To">
							<div class="input-group-append">
								<span class="input-group-text cursor-pointer calendar-button" data-target="accessEndDate"><i class="fa-solid fa-calendar"></i></span>
							</div>
						</div>
					</div>
					<div class="col-sm-2">Central Time</div>
				</div>
			</div>
		</div>

		<div class="form-group row">
			<div class="col-sm-12">
				#application.objWebEditor.showContentBoxWithLinks(fieldname='accessExpirationContent', fieldlabel='Expiration Message: *', contentID=arguments.event.getValue('accessExpirationContentID'), content=arguments.event.getValue('accessExpirationContent'), allowMergeCodes=1, supportsBootstrap=true, allowVersioning=true)#
			</div>
		</div>
	</div>
	<div id="err_onholdnotify_emails" class="alert alert-danger mb-2 d-none"></div>
	<div class="form-group row mt-2">
		<label for="onHoldNotificationEmails" class="col-sm-3 col-form-label-sm font-size-md">On-Hold Notification Email(s):</label>
		<div class="col-sm-9">
			<input type="text" name="onHoldNotificationEmails" id="onHoldNotificationEmails" class="form-control form-control-sm" value="#replace(arguments.event.getValue('onHoldNotificationEmails'),chr(34),'&quot;','ALL')#" maxlength="400">
			<div class="form-text small">
				(one or more e-mail addresses)
			</div>
		</div>
	</div>

	<h5 class="mt-4">Project Description and Instructions</h5>

	<div class="form-group row">
		<div class="col-sm-12">
			#application.objWebEditor.showContentBoxWithLinks(fieldname='projectDesc', fieldlabel='Project Description:', contentID=arguments.event.getValue('projectContentID'), content=arguments.event.getValue('projectDesc'), allowMergeCodes=1, supportsBootstrap=true, allowVersioning=true)#
		</div>
	</div>

	<div class="form-group row mt-3">
		<div class="col-sm-12">
			#application.objWebEditor.showContentBoxWithLinks(fieldname='instructionContent', fieldlabel='Instructions for #local.qryWorkspaces.solicitorFieldLabel#:', contentID=arguments.event.getValue('instructionContentID'), content=arguments.event.getValue('instructionContent'), allowMergeCodes=1, supportsBootstrap=true, allowVersioning=true)#
		</div>
	</div>

	<div class="form-group row mt-3">
		<div class="col-sm-12">
			#application.objWebEditor.showContentBoxWithLinks(fieldname='chooseProspectsInstructionContent', fieldlabel='Instructions for the #local.qryWorkspaces.solicitorFieldLabel# to choose #local.qryWorkspaces.prospectFieldLabelPlural#:', contentID=arguments.event.getValue('chooseProspectsInstructionContentID'), content=arguments.event.getValue('chooseProspectsInstructionContent'), allowMergeCodes=1, supportsBootstrap=true, allowVersioning=true)#
		</div>
	</div>

	<div class="form-group row">
		<label class="col-sm-3 col-form-label-sm font-size-md pt-0">Request Member Payment Profile: *</label>
		<div class="col-sm-9">
			<div class="form-check form-check-inline">
				<input type="radio" name="requestPayProfile" id="requestPayProfileYes" class="form-check-input" value="1" onclick="togglePayProfileSettings(1);"<cfif arguments.event.getValue('requestPayProfile') is 1> checked</cfif>>
				<label class="form-check-label" for="requestPayProfileYes">Yes</label>
			</div>
			<div class="form-check form-check-inline">
				<input type="radio" name="requestPayProfile" id="requestPayProfileNo" class="form-check-input" value="0" onclick="togglePayProfileSettings(0);"<cfif arguments.event.getValue('requestPayProfile') is 0> checked</cfif>>
				<label class="form-check-label" for="requestPayProfileNo">No</label>
			</div>
		</div>
	</div>

	<div class="payProfileRow<cfif arguments.event.getValue('requestPayProfile') is 0> d-none</cfif>">
		<div class="form-group row">
			<label for="profileID" class="col-sm-3 col-form-label-sm font-size-md">Payment Profile: *</label>
			<div class="col-sm-9">
				<select name="profileID" id="profileID" class="form-control form-control-sm">
					<option value="0">Select a Payment Profile</option>
					<cfloop query="local.qryPaymentProfiles">
						<option value="#local.qryPaymentProfiles.profileID#"<cfif local.qryPaymentProfiles.profileID eq arguments.event.getValue('profileID')> selected="selected"</cfif>>#local.qryPaymentProfiles.profileName#</option>
					</cfloop>
				</select>
			</div>
		</div>
		<div id="err_payprofileaddnotify_emails" class="alert alert-danger mb-2 d-none"></div>
		<div class="form-group row">
			<label for="payProfileNotificationEmails" class="col-sm-3 col-form-label-sm font-size-md">Payment Profile Added Notification Email(s):</label>
			<div class="col-sm-9">
				<input type="text" name="payProfileNotificationEmails" id="payProfileNotificationEmails" class="form-control form-control-sm" value="#replace(arguments.event.getValue('payProfileNotificationEmails'),chr(34),'&quot;','ALL')#" maxlength="400">
				<div class="form-text small">
					(one or more e-mail addresses)
				</div>
			</div>
		</div>

		<div class="form-group row">
			<div class="col-sm-12">
				#application.objWebEditor.showContentBoxWithLinks(fieldname='requestPayProfileContent', fieldlabel='Payment Instructions: *', contentID=arguments.event.getValue('requestPayProfileContentID'), content=arguments.event.getValue('requestPayProfileContent'), allowMergeCodes=1, supportsBootstrap=true, allowVersioning=true)#
			</div>
		</div>
	</div>


	<cfif arguments.event.getValue('projectID') gt 0>
		<h5 class="mt-4">Featured Objective Fields and Stats Objective Fields</h5>

		<div class="form-group row">
			<label for="projectFeaturedFields" class="col-sm-3 col-form-label-sm font-size-md">Objective Fields to Feature:</label>
			<div class="col-sm-9">
				<select name="projectFeaturedFields" id="projectFeaturedFields" multiple="multiple" class="form-control form-control-sm" data-toggle="custom-select2" placeholder="Select Fields">
					<cfloop query="local.qryProjectFields">
						<option value="#local.qryProjectFields.fieldID#" <cfif listFindNoCase(valueList(local.qryProjectFeaturedFields.fieldID), local.qryProjectFields.fieldID)>selected</cfif>>#local.qryProjectFields.fieldText#</option>
					</cfloop>
				</select>
			</div>
		</div>

		<div class="form-group row">
			<label for="projectStatsFields" class="col-sm-3 col-form-label-sm font-size-md">Objective Fields to Include in Summary:</label>
			<div class="col-sm-9">
				<select name="projectStatsFields" id="projectStatsFields" multiple="multiple" class="form-control form-control-sm" data-toggle="custom-select2" placeholder="Select Fields">
					<cfloop query="local.qryProjectFields">
						<option value="#local.qryProjectFields.fieldID#" <cfif listFindNoCase(valueList(local.qryProjectStatsFields.fieldID), local.qryProjectFields.fieldID)>selected</cfif>>#local.qryProjectFields.fieldText#</option>
					</cfloop>
				</select>
			</div>
		</div>

		<h5 class="mt-4">#local.qryProject.prospectFieldLabel# and #local.qryProject.solicitorFieldLabel# Display settings</h5>

		<div class="form-group row">
			<label for="" class="col-sm-3 col-form-label-sm font-size-md">Field Set:</label>
			<div class="col-sm-9">
				#local.strProspectAndSolicitorFSSelector.html#
				<div class="form-text small">(First Name, Last Name, MemberNumber, and Company always appear.)</div>
			</div>
		</div>

		<div class="form-group row mt-2">
			<label for="showMemberPhotos" class="col-sm-3 col-form-label-sm font-size-md">Show Member Photos<br />when showing search results:</label>
			<div class="col-sm-4">
				<cfselect name="showMemberPhotos" id="showMemberPhotos" class="form-control form-control-sm">
					<option value="1" <cfif arguments.event.getValue('showMemberPhotos') is 1>selected</cfif>>Yes, Show Photos</option>
					<option value="0" <cfif arguments.event.getValue('showMemberPhotos') is 0>selected</cfif>>No, Do Not Show Photos</option>
				</cfselect>
			</div>
		</div>

		<div class="mt-3">
			<h5>Group Sets to Feature for #local.qryProject.prospectFieldLabelPlural#</h5>
			<div class="mt-2">
				#local.detailsGroupSetWidget.html#
			</div>
		</div>

		<h5 class="mt-4 mb-1">Select Email Templates to Include in This Project</h5>
		<p>Which of your defined Task Email Template do you want your #local.qryWorkspaces.solicitorFieldLabel# to be able to send to their #local.qryWorkspaces.prospectFieldLabelPlural#?</p>

		<div id="stepTemplatesDIV" class="my-3">
			<div class="ml-4 border">
				<table width="100%">
				<tr valign="top">
					<td style="width:50%;">
						<div id="mcg_gridbox_ET" class="gridbox gridbox_modern" style="cursor:default;">
							<div class="xhdr" style="width:100%; height:25px; overflow:hidden; position:relative;">
								<table class="hdr" style="width:100%;table-layout:fixed; " cellpadding="0" cellspacing="0">
								<tbody>
								<tr style="height:auto;">
									<th style="height:0px;"></th>
								</tr>
								<tr>
									<td><div class="hdrcell">Templates Visible to Solicitors</div></td>
								</tr>
								</tbody>
								</table>
							</div>
							<div class="objbox" style="width:100%;overflow:auto;max-height:175px;background-color:##EDEDED;">
								<table class="obj row20px" style="width:100%;table-layout:fixed;" cellpadding="0" cellspacing="0">
								<tbody>
								<tr style="height:auto;">
									<th style="height:0px;"></th>
									<th style="height:0px; width:25px;"></th>
								</tr>
								</tbody>
								<tbody id="stepETTasks_sel">
									#local.strEmailTemplate.selgrid#
								</tbody>
								</table>
							</div>
						</div>
					</td>
					<td style="width:50%;">
						<div class="gridbox gridbox_modern" style="cursor:default;">
							<div class="xhdr" style="width:100%; height:25px; overflow:hidden; position:relative;">
								<table class="hdr" style="width:100%;table-layout:fixed;" cellpadding="0" cellspacing="0">
								<tbody>
								<tr style="height:auto;">
									<th style="height:0px;"></th>
								</tr>
								<tr>
									<td><div class="hdrcell">Non-Selected Templates</div></td>
								</tr>
								</tbody>
								</table>
							</div>
							<div class="objbox" style="width:100%;overflow:auto;max-height:175px;background-color:##EDEDED;">
								<table class="obj" style="width:100%;table-layout:fixed;" cellpadding="0" cellspacing="0">
								<tbody >
								<tr style="height:auto;">
									<th style="height:0px;"></th>
									<th style="height:0px; width:25px;"></th>
								</tr>
								</tbody>
								<tbody id="stepETTasks_avail">
									#local.strEmailTemplate.availgrid#
								</tbody>
								</table>
							</div>									
						</div>
					</td>
				</tr>
				</table>
			</div>
		</div>
	</cfif>

	<cfif local.strWorkspaceProjectFields.hasFields>
		<h5>Project Summary Fields</h5>
		#local.strWorkspaceProjectFields.html#
	</cfif>
</cfform>
</cfoutput>
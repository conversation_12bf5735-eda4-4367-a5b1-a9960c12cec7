<cfsavecontent variable="local.classJS">
	<cfoutput>
	<script language="JavaScript">
		let classificationTable;

		function getMemberDirectoryClassificationsData_memberDirectoryGroupSets(onCompleteFunc) {
			var ajaxParams = { memberDirectoryID: #arguments.event.getValue('mdID')# };
			TS_AJX('ADMMEMBERDIRECTORY','getAvailableAndSelectedMemberDirectoryGroupSetsJSON',ajaxParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
		}
		
		//Overwritting Group Set Selector editClassification_memberDirectoryGroupSets() to pass mdid
		function editClassification_memberDirectoryGroupSets(gsid,cid) {
			MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: (cid !== 0) ? 'Edit Classification' : 'Add Classification',
			iframe: true,
			contenturl: '#this.link.editClassification#&mdid=#arguments.event.getValue('mdID')#&classificationID=' + cid+'&groupSetID='+gsid,
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: false,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmClassification :submit").click',
				extrabuttonlabel: 'Save Details'
			}
		});
		}
		
		function reloadMemberDirectoryGroupSets() {
			loadGroupSetGrids_memberDirectoryGroupSets();
		}

		function deleteMemberDirectoryClassification(classificationID, onCompleteFunc) {
			var objParams = {
				memberDirectoryID: #arguments.event.getValue('mdID')#,
				memberDirectorySRID: #local.siteResourceID#,
				classificationID: classificationID
			};
			TS_AJX('ADMMEMBERDIRECTORY','removeClassification',objParams,onCompleteFunc,onCompleteFunc,10000,onCompleteFunc);
		}

		function moveMemberDirectoryClassification(classificationID, direction, onCompleteFunc) {
			var objParams = {
				memberDirectoryID: #arguments.event.getValue('mdID')#,
				classificationID: classificationID,
				dir: direction
			};
			TS_AJX('ADMMEMBERDIRECTORY','doMoveClassification',objParams,onCompleteFunc,onCompleteFunc,10000,onCompleteFunc);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.classJS#">
<cfoutput>
<div class="card mb-1">
	<div class="card-header bg-light p-1">
		<div class="card-header--title font-size-lg ml-2">Classifications</div>
	</div>
	<div class="card-body p-1">
		#local.memberDirectoryGroupSetWidget.html#
	</div>
</div>
</cfoutput>